{"url": "http://localhost:2368", "server": {"port": 2368, "host": "0.0.0.0"}, "database": {"client": "sqlite3", "connection": {"filename": "content/data/ghost.db"}}, "mail": {"transport": "Direct"}, "logging": {"transports": ["file", "stdout"]}, "process": "systemd", "paths": {"contentPath": "/root/ghost/ghost-content/"}, "uploads": {"maxFileSize": 104857600, "icons": {"maxFileSize": 10485760}, "images": {"maxFileSize": 104857600}, "media": {"maxFileSize": 104857600}, "files": {"maxFileSize": 104857600}}}