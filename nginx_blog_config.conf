server {
    server_name blog.xing2006.me;
    
    # Increase max upload size to 100MB
    client_max_body_size 100M;

    location / {
        proxy_pass http://127.0.0.1:2368;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_buffering off;
    }

    # Optional: Add some security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src * data: 'unsafe-eval' 'unsafe-inline'" always;

    listen 443 ssl; # managed by Certbot
    ssl_certificate /etc/letsencrypt/live/blog.xing2006.me/fullchain.pem; # managed by Certbot
    ssl_certificate_key /etc/letsencrypt/live/blog.xing2006.me/privkey.pem; # managed by <PERSON>rtbot
    include /etc/letsencrypt/options-ssl-nginx.conf; # managed by Certbot
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem; # managed by Certbot

}
server {
    if ($host = blog.xing2006.me) {
        return 301 https://$host$request_uri;
    } # managed by Certbot


    listen 80;
    server_name blog.xing2006.me;
    return 404; # managed by Certbot


}
